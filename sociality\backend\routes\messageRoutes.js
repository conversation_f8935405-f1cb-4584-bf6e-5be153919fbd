import express from "express";
import protectRoute from "../middlewares/protectRoute.js";
import { uploadFields, uploadSingle, handleMulterError } from "../middlewares/multerConfig.js";
import { getMessages, sendMessage, getConversations, deleteMessage } from "../controllers/messageController.js";

const router = express.Router();

router.get("/conversations", protectRoute, getConversations);
router.get("/:otherUserId", protectRoute, getMessages);
router.post("/", protectRoute, uploadFields, handleMulterError, sendMessage);
router.post("/upload-image", protectRoute, uploadSingle, handleMulterError, sendMessage);
router.delete("/:messageId", protectRoute, deleteMessage);

export default router;
