# Image Upload in Messages - Backend Implementation

## Overview
This document describes the backend implementation for image upload functionality in messages. Users can now send images along with their text messages.

## Features Implemented

### 1. Multer Configuration (`middlewares/multerConfig.js`)
- **Memory Storage**: Images are stored in memory temporarily before uploading to Cloudinary
- **File Filtering**: Only image files are allowed (checked by MIME type)
- **Size Limits**: Maximum file size of 5MB
- **Error Handling**: Comprehensive error handling for various upload scenarios

### 2. Updated Message Routes (`routes/messageRoutes.js`)
- **Main Route**: `POST /api/messages/` - Handles both text and image messages
- **Dedicated Route**: `POST /api/messages/upload-image` - Specific endpoint for image uploads
- **Middleware Chain**: `protectRoute` → `uploadSingle` → `handleMulterError` → `sendMessage`

### 3. Enhanced Message Controller (`controllers/messageController.js`)
- **Multer Integration**: Handles `req.file` from multer middleware
- **Base64 Conversion**: Converts buffer to base64 for Cloudinary upload
- **Backward Compatibility**: Maintains support for existing base64 and FormData uploads
- **Error Handling**: Proper error responses for upload failures

## How It Works

### Frontend to Backend Flow
1. **Frontend**: User selects an image using the file input
2. **Frontend**: Creates FormData with the image file and other message data
3. **Backend**: Multer middleware processes the multipart form data
4. **Backend**: File is stored in memory as a buffer
5. **Backend**: Buffer is converted to base64 for Cloudinary upload
6. **Backend**: Image URL is stored in the message document
7. **Backend**: Message is saved to database and sent via Socket.IO

### Supported Upload Methods
1. **FormData with File**: New method using multer (recommended)
2. **Base64 String**: Legacy method for backward compatibility
3. **Blob URLs**: Converted to files before upload

### Error Handling
- **File Size**: Returns 400 error if file exceeds 5MB
- **File Type**: Returns 400 error if file is not an image
- **Upload Failure**: Returns 500 error if Cloudinary upload fails
- **Missing Data**: Returns 400 error for empty messages

## API Endpoints

### Send Message with Image
```
POST /api/messages/
Content-Type: multipart/form-data

Fields:
- text: string (optional)
- recipientId: string (required)
- img: file (optional, image file)
- emoji: string (optional)
```

### Response Format
```json
{
  "_id": "message_id",
  "conversationId": "conversation_id",
  "sender": "sender_id",
  "text": "message text",
  "img": "https://cloudinary.com/image_url",
  "createdAt": "timestamp"
}
```

## Configuration

### Multer Settings
- **Storage**: Memory storage (no disk writes)
- **File Size Limit**: 5MB
- **File Count**: 1 file per request
- **Allowed Types**: Images only (image/*)

### Cloudinary Integration
- Images are uploaded to Cloudinary after multer processing
- Original file buffer is converted to base64 format
- Secure URLs are returned and stored in database

## Security Considerations
- **File Type Validation**: Only image MIME types are allowed
- **Size Limits**: 5MB maximum to prevent abuse
- **Authentication**: All routes require valid JWT token
- **Input Sanitization**: File data is validated before processing

## Testing
To test the image upload functionality:

1. Start the backend server: `npm run dev`
2. Use the frontend message input to select an image
3. Send a message with the image attached
4. Verify the image appears in the conversation
5. Check that the image URL is stored in the database

## Troubleshooting

### Common Issues
1. **"Only image files are allowed!"**: Check file MIME type
2. **"File too large"**: Reduce image size below 5MB
3. **"Image upload failed"**: Check Cloudinary configuration
4. **"Cannot send empty message"**: Include text or image content

### Debug Steps
1. Check server logs for detailed error messages
2. Verify Cloudinary credentials in environment variables
3. Test with different image formats (JPEG, PNG, GIF)
4. Ensure proper Content-Type headers in requests
