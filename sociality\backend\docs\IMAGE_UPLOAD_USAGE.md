# Image Upload in Messages - Frontend Integration Guide

## Overview
Your backend now supports image uploads in messages through multiple methods. Here's how to integrate it with your frontend.

## Available Endpoints

### 1. Main Message Endpoint (Recommended)
**POST** `/api/messages/`

This endpoint supports both text and image messages with multiple upload methods:

#### Method A: FormData with File Upload (Recommended for your frontend)
```javascript
const formData = new FormData();
formData.append('recipientId', recipientUserId);
formData.append('text', messageText); // Optional
formData.append('img', imageFile); // File object from input[type="file"]

const response = await fetch('/api/messages/', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`, // Your JWT token
  },
  body: formData
});
```

#### Method B: JSON with Base64 Image
```javascript
const response = await fetch('/api/messages/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bear<PERSON> ${token}`,
  },
  body: JSON.stringify({
    recipientId: recipientUserId,
    text: messageText, // Optional
    img: base64ImageString // "data:image/jpeg;base64,..."
  })
});
```

### 2. Dedicated Image Upload Endpoint
**POST** `/api/messages/upload-image`

For image-only messages:
```javascript
const formData = new FormData();
formData.append('recipientId', recipientUserId);
formData.append('img', imageFile);

const response = await fetch('/api/messages/upload-image', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
  body: formData
});
```

## Frontend Implementation Example

### HTML Input
```html
<input type="file" id="imageInput" accept="image/*" />
<button onclick="sendImageMessage()">Send Image</button>
```

### JavaScript Function
```javascript
async function sendImageMessage() {
  const fileInput = document.getElementById('imageInput');
  const file = fileInput.files[0];
  
  if (!file) {
    alert('Please select an image');
    return;
  }

  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('Please select a valid image file');
    return;
  }

  // Validate file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    alert('Image size must be less than 5MB');
    return;
  }

  const formData = new FormData();
  formData.append('recipientId', currentRecipientId); // Your recipient ID
  formData.append('text', 'Check out this image!'); // Optional text
  formData.append('img', file);

  try {
    const response = await fetch('/api/messages/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${yourJWTToken}`,
      },
      body: formData
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Message sent successfully:', result);
      // Update your UI with the new message
    } else {
      const error = await response.json();
      console.error('Error sending message:', error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}
```

## File Upload Constraints

- **File Types**: Only image files are allowed (image/*)
- **File Size**: Maximum 5MB per image
- **File Count**: 1 image per request
- **Authentication**: JWT token required in Authorization header

## Error Handling

The backend will return appropriate error messages:

- `400`: File too large (>5MB)
- `400`: Invalid file type (non-image)
- `400`: Too many files
- `401`: Unauthorized (invalid/missing JWT)
- `500`: Server error (Cloudinary upload failed, etc.)

## Response Format

Successful response:
```json
{
  "_id": "message_id",
  "sender": "sender_id",
  "text": "message_text",
  "img": "https://cloudinary_url/image.jpg",
  "conversationId": "conversation_id",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "seen": false
}
```

## Integration with Your Existing Frontend

Since you mentioned you already implemented the frontend, you just need to:

1. **Update your form submission** to use FormData instead of JSON when sending images
2. **Add the image file** to the FormData object
3. **Remove Content-Type header** when using FormData (let the browser set it)
4. **Handle the image URL** in the response to display the uploaded image

The backend is now ready to receive image uploads from your frontend implementation!
