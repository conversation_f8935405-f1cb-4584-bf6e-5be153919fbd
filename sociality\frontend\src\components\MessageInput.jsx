import {
	Flex,
	Input,
	InputGroup,
	InputRightElement,
	InputLeftElement,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	Spinner,
	useDisclosure,
	Tooltip,
	Box,
	Text,
	Button,
	Icon,
	IconButton,
	Divider,
	Progress,
	Popover,
	PopoverTrigger,
	PopoverContent,
	PopoverBody,
	Portal,
	useColorModeValue,
	Textarea,
	HStack,
	VStack,
} from "@chakra-ui/react";
import { IoSendSharp, IoAttach, IoMicOutline, IoHappyOutline, IoImageOutline } from "react-icons/io5";
import { FaPaperclip, FaSmile, FaMicrophone } from "react-icons/fa";
import { MdEmojiEmotions, MdAttachFile } from "react-icons/md";
import { CloseIcon } from "@chakra-ui/icons";
import useShowToast from "../hooks/useShowToast";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { user<PERSON>tom, conversations<PERSON><PERSON>, selected<PERSON>onversation<PERSON><PERSON> } from "../atoms";
import usePreviewImg from "../hooks/usePreviewImg";
import { useSocket } from "../hooks/useSocket";
import { memo, useRef, useState, useCallback, useEffect } from "react";
import { fetchWithSession } from "../utils/api";
import "../styles/telegram-input.css";


const MessageInput = memo(({ setMessages }) => {
	const [messageText, setMessageText] = useState("");
	const [isRecording, setIsRecording] = useState(false);
	const [showAttachMenu, setShowAttachMenu] = useState(false);

	// Debug state changes
	useEffect(() => {
		console.log('showAttachMenu state changed to:', showAttachMenu);
	}, [showAttachMenu]);

	// Close attachment menu when clicking outside
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (showAttachMenu && !event.target.closest('.attachment-menu-container')) {
				setShowAttachMenu(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [showAttachMenu]);
	const [textareaHeight, setTextareaHeight] = useState(40);
	const textareaRef = useRef(null);
	const fileInputRef = useRef(null);
	const showToast = useShowToast();
	const selectedConversation = useRecoilValue(selectedConversationAtom);
	const setConversations = useSetRecoilState(conversationsAtom);
	const currentUser = useRecoilValue(userAtom);
	const { socket, isConnected } = useSocket();
	const { isOpen: isImageOpen, onOpen: onImageOpen, onClose: onImageClose } = useDisclosure();
	const { isOpen: isEmojiOpen, onOpen: onEmojiOpen, onClose: onEmojiClose } = useDisclosure();
	const { handleImageChange, imgUrl, setImgUrl, selectedFile, setSelectedFile } = usePreviewImg();
	const [isSending, setIsSending] = useState(false);
	const [selectedEmoji, setSelectedEmoji] = useState("");

	// Theme-aware colors
	const inputBgColor = useColorModeValue("#f8f9fa", "#2d2d2d");
	const inputBorderColor = useColorModeValue("#e0e0e0", "#404040");
	const textColor = useColorModeValue("gray.800", "white");
	const placeholderColor = useColorModeValue("gray.500", "gray.400");
	const buttonHoverBg = useColorModeValue("gray.100", "whiteAlpha.100");
	const containerBg = useColorModeValue("white", "#1a1a1a");
	const borderTopColor = useColorModeValue("gray.200", "gray.700");
	const emojiPickerTextColor = useColorModeValue("gray.800", "whiteAlpha.900");
	const menuButtonColor = useColorModeValue("gray.600", "whiteAlpha.800");
	const menuButtonHoverBg = useColorModeValue("rgba(59, 130, 246, 0.1)", "rgba(59, 130, 246, 0.1)");
	const menuButtonHoverColor = useColorModeValue("#3b82f6", "#3b82f6");

	// Auto-resize textarea
	useEffect(() => {
		if (textareaRef.current) {
			textareaRef.current.style.height = 'auto';
			const scrollHeight = textareaRef.current.scrollHeight;
			const newHeight = Math.min(Math.max(scrollHeight, 40), 120); // Min 40px, max 120px
			setTextareaHeight(newHeight);
			textareaRef.current.style.height = `${newHeight}px`;
		}
	}, [messageText]);



	// Handle file attachment
	const handleFileAttachment = useCallback(() => {
		fileInputRef.current?.click();
	}, []);

	// Handle image attachment
	const handleImageAttachment = useCallback(() => {
		console.log('Image attachment clicked');
		try {
			const input = document.createElement('input');
			input.type = 'file';
			input.accept = 'image/*';
			input.onchange = (e) => {
				console.log('File selected:', e.target.files[0]);
				try {
					handleImageChange(e);
				} catch (error) {
					console.error('Error in handleImageChange:', error);
					showToast("Error", "Failed to process image", "error");
				}
			};
			input.onerror = (e) => {
				console.error('File input error:', e);
			};
			input.click();
			setShowAttachMenu(false); // Close the menu after clicking
		} catch (error) {
			console.error('Error creating file input:', error);
			showToast("Error", "Failed to open file dialog", "error");
		}
	}, [handleImageChange, showToast]);

	// Handle voice recording (placeholder)
	const handleVoiceRecording = useCallback(() => {
		setIsRecording(!isRecording);
		// Add actual voice recording logic here
		showToast("Info", "Voice recording feature coming soon!", "info");
	}, [isRecording, showToast]);

	// Emoji list (simplified for demo)
	const emojiList = ["😀", "😂", "😍", "🤔", "😢", "😡", "👍", "👎", "❤️", "🔥", "✨", "🎉"];

	// Define sendRequestFn to handle both regular and federated messages
	const sendRequestFn = async (formData, messageData) => {
		let responseData = null;

		// Handle federated messages
		if (selectedConversation.isFederated) {
			console.log("Sending federated message:", messageData.text);
			const response = await fetchWithSession("/api/cross-platform/rooms/" + selectedConversation._id + "/messages", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					message: messageData.text
				}),
			});
			responseData = await response.json();
			console.log("Federated message response:", responseData);

			// For federated messages, transform the response to match expected format
			if (responseData.success && responseData.localMessage) {
				return {
					_id: responseData.localMessage.id,
					text: responseData.localMessage.text,
					sender: responseData.localMessage.sender._id,
					senderUsername: responseData.localMessage.sender.username,
					senderPlatform: responseData.localMessage.sender.platform,
					createdAt: responseData.localMessage.timestamp,
					isFederated: true,
					platform: responseData.localMessage.platform,
					tempId: messageData.tempId
				};
			}
		} else {
			// Handle regular messages
			if (formData) {
				const response = await fetchWithSession("/api/messages", {
					method: "POST",
					body: formData,
				});
				responseData = await response.json();
			} else {
				const response = await fetchWithSession("/api/messages", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(messageData),
				});
				responseData = await response.json();
			}
		}

		console.log("Message sent successfully (server response):", responseData);
		return responseData;
	};

	// PATCH: Define messageAlreadyUpdated utility
	const messageAlreadyUpdated = (prev, tempId, responseData) => {
		return prev.some(msg =>
			(msg.tempId === tempId && !msg.isOptimistic) ||
			(responseData._id && msg._id === responseData._id)
		);
	};

	// Handle message submission
	const handleSendMessage = useCallback(async (e) => {
		e?.preventDefault();
		if (isSending) return;
		if (!messageText.trim() && !imgUrl && !selectedEmoji) {
			console.warn('Attempted to send empty message. Aborting.');
			return;
		}
		setIsSending(true);
		try {
			const tempId = Date.now().toString();
			let formData = null;
			let messageData = {
				tempId,
				text: messageText,
				recipientId: selectedConversation.userId,
				img: imgUrl || undefined,
				emoji: selectedEmoji || undefined,
			};

			// For federated messages, don't use images/emojis for now
			if (selectedConversation.isFederated && (imgUrl || selectedEmoji)) {
				showToast("Info", "Images and emojis are not supported in cross-platform rooms yet", "info");
				setIsSending(false);
				return;
			}
			// Prepare message with any media type
			if (imgUrl && selectedFile) {
				console.log('Creating FormData with file:', selectedFile);
				formData = new FormData();
				formData.append("text", messageText);
				formData.append("recipientId", selectedConversation.userId);
				formData.append("img", selectedFile); // Use the actual file object
				if (selectedEmoji) {
					formData.append("emoji", selectedEmoji);
				}
				console.log('FormData created, entries:', Array.from(formData.entries()));
			}
			const optimisticMessage = selectedConversation.isFederated ? {
				_id: tempId,
				text: messageText,
				sender: currentUser._id,
				senderUsername: currentUser.name || currentUser.username,
				senderPlatform: 'sociality',
				createdAt: new Date().toISOString(),
				isOptimistic: true,
				isNew: true,
				isFederated: true,
				platform: 'sociality',
				tempId
			} : {
				text: messageText,
				sender: currentUser._id,
				tempId,
				createdAt: new Date().toISOString(),
				isOptimistic: true,
				isNew: true,
				img: imgUrl || undefined,
				emoji: selectedEmoji || undefined,
			};
			setMessages(prev => [...prev, optimisticMessage]);

			// Immediate aggressive scroll trigger for optimistic message
			const forceImmediateScroll = () => {
				const messageContainer = document.getElementById('messageListContainer');
				if (messageContainer) {
					console.log('📤 Immediate aggressive scroll after sending message');
					// Force scroll to absolute maximum
					messageContainer.scrollTop = messageContainer.scrollHeight;
					console.log('📤 Set scrollTop to:', messageContainer.scrollTop);
				}
			};

			// Multiple immediate scroll attempts
			setTimeout(forceImmediateScroll, 10);
			setTimeout(forceImmediateScroll, 50);
			setTimeout(forceImmediateScroll, 100);

			try {
				console.log("Waiting for server to process message...");
				const responseData = await sendRequestFn(formData, messageData);
				setMessages(prev => {
					if (messageAlreadyUpdated(prev, tempId, responseData)) {
						console.log("Message already updated by socket, skipping update");
						return prev;
					}
					const updatedMessages = prev.map(msg =>
						msg.tempId === tempId ? { ...responseData, isNew: true } : msg
					);

					return updatedMessages;
				});
				setConversations(prev => {
					const updatedConversations = [...prev];
					const conversationIndex = updatedConversations.findIndex(c => c._id === selectedConversation._id);
					if (conversationIndex !== -1) {
						// Create the lastMessage object based on the actual response
						const lastMessageData = {
							text: responseData.text || messageText,
							sender: currentUser._id,
							createdAt: responseData.createdAt || new Date().toISOString(),
						};

						// Add image URL if present in response
						if (responseData.img) {
							lastMessageData.img = responseData.img;
						}

						// Add emoji if present
						if (responseData.emoji || selectedEmoji) {
							lastMessageData.emoji = responseData.emoji || selectedEmoji;
						}

						updatedConversations[conversationIndex] = {
							...updatedConversations[conversationIndex],
							lastMessage: lastMessageData,
						};
						const conversation = updatedConversations.splice(conversationIndex, 1)[0];
						updatedConversations.unshift(conversation);
					}
					return updatedConversations;
				});


			} catch (error) {
				showToast("Error", error.message, "error");
			}
			document.getElementById('messageInput')?.blur();
			setMessageText("");
			setImgUrl("");
			setSelectedFile(null);
			setSelectedEmoji("");
		} catch (error) {
			showToast("Error", error.message, "error");
		} finally {
			setIsSending(false);
		}
	}, [
		messageText,
		imgUrl,
		selectedFile,
		selectedEmoji,
		selectedConversation?.userId,
		selectedConversation?.isFederated,
		currentUser?._id,
		setMessages,
		showToast,
		setImgUrl,
		setSelectedFile,
		setConversations
	]);

	// Handle Enter key press
	const handleKeyDown = useCallback((e) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			console.log('Enter key pressed, message text:', messageText);
			handleSendMessage(e);
		}
	}, [handleSendMessage, messageText]);

	return (
		<Box
			bg={containerBg}
			p={2} // Reduced padding to minimize extra space
		>
			{/* Image preview */}
			{imgUrl && (
				<Box mb={3} className="telegram-image-preview">
					<HStack spacing={3}>
						<Box
							w="60px"
							h="60px"
							borderRadius="md"
							overflow="hidden"
							bg={useColorModeValue("gray.200", "gray.100")}
							backgroundImage={`url(${imgUrl})`}
							backgroundSize="cover"
							backgroundPosition="center"
						/>
						<VStack align="start" spacing={1} flex={1}>
							<Text fontSize="sm" fontWeight="medium" color={textColor}>
								Image Preview
							</Text>
							<Text fontSize="xs" color={useColorModeValue("gray.600", "gray.500")}>
								Image ready to send
							</Text>
						</VStack>
						<IconButton
							icon={<CloseIcon />}
							size="sm"
							variant="ghost"
							onClick={() => {
								setImgUrl("");
								setSelectedFile(null);
							}}
							aria-label="Remove image"
						/>
					</HStack>
				</Box>
			)}

			<Flex gap={2} alignItems="flex-end">
				{/* Attachment button with simple menu */}
				<Box position="relative" className="attachment-menu-container">
					<IconButton
						icon={<MdAttachFile />}
						size="lg"
						variant="ghost"
						colorScheme="gray"
						borderRadius="full"
						onClick={() => {
							console.log('Attachment button clicked, current state:', showAttachMenu);
							setShowAttachMenu(!showAttachMenu);
						}}
						className="telegram-attach-button"
						aria-label="Attach file"
						h="44px"
						w="44px"
					/>

					{/* Simple attachment menu */}
					{showAttachMenu && (
						<Box
							position="absolute"
							bottom="50px"
							left="0"
							bg={containerBg}
							border="1px solid"
							borderColor={useColorModeValue("gray.200", "gray.600")}
							borderRadius="md"
							boxShadow="lg"
							p={2}
							w="200px"
							zIndex={1000}
						>
							<VStack spacing={1}>
								<Button
									leftIcon={<IoImageOutline />}
									variant="ghost"
									size="sm"
									w="full"
									justifyContent="flex-start"
									onClick={() => {
										console.log('Photo button clicked');
										handleImageAttachment();
									}}
									_hover={{ bg: menuButtonHoverBg, color: menuButtonHoverColor }}
									color={menuButtonColor}
								>
									Photo
								</Button>
								<Button
									leftIcon={<FaPaperclip />}
									variant="ghost"
									size="sm"
									w="full"
									justifyContent="flex-start"
									onClick={handleFileAttachment}
									_hover={{ bg: menuButtonHoverBg, color: menuButtonHoverColor }}
									color={menuButtonColor}
								>
									File
								</Button>
							</VStack>
						</Box>
					)}
				</Box>

				{/* Main input area */}
				<Box
					flex={1}
					className="telegram-input-container"
					borderRadius="24px"
					p={2}
				>
					<HStack spacing={2} align="flex-end">
						{/* Emoji button */}
						<Popover
							isOpen={isEmojiOpen}
							onClose={onEmojiClose}
							placement="top-start"
						>
							<PopoverTrigger>
								<IconButton
									icon={<MdEmojiEmotions />}
									size="sm"
									variant="ghost"
									colorScheme="gray"
									borderRadius="full"
									onClick={onEmojiOpen}
									className="telegram-emoji-button"
									aria-label="Add emoji"
								/>
							</PopoverTrigger>
							<Portal>
								<PopoverContent
									w="280px"
									className="telegram-emoji-picker"
								>
									<PopoverBody p={3}>
										<Text fontSize="sm" fontWeight="medium" mb={2} color={emojiPickerTextColor}>
											Quick Emojis
										</Text>
										<Flex wrap="wrap" gap={2}>
											{emojiList.map((emoji, index) => (
												<Button
													key={index}
													variant="ghost"
													size="sm"
													fontSize="lg"
													onClick={() => {
														setMessageText(prev => prev + emoji);
														onEmojiClose();
													}}
													_hover={{ bg: useColorModeValue("gray.100", "rgba(255, 255, 255, 0.1)") }}
													borderRadius="md"
													p={2}
													color={useColorModeValue("gray.700", "white")}
												>
													{emoji}
												</Button>
											))}
										</Flex>
									</PopoverBody>
								</PopoverContent>
							</Portal>
						</Popover>

						{/* Text input */}
						<Textarea
							ref={textareaRef}
							value={messageText}
							onChange={(e) => setMessageText(e.target.value)}
							onKeyDown={handleKeyDown}
							placeholder="Type a message..."
							className="telegram-textarea"
							minH="40px"
							maxH="120px"
							color={textColor}
							_placeholder={{ color: placeholderColor }}
							fontSize="15px"
							lineHeight="1.4"
							py={2}
							px={1}
							style={{ height: `${textareaHeight}px` }}
						/>
					</HStack>
				</Box>

				{/* Send/Voice button */}
				{messageText.trim() || imgUrl ? (
					<IconButton
						icon={isSending ? <Spinner size="sm" /> : <IoSendSharp />}
						size="lg"
						borderRadius="full"
						onClick={handleSendMessage}
						isDisabled={isSending}
						className="telegram-send-button"
						aria-label="Send message"
						h="44px"
						w="44px"
					/>
				) : (
					<IconButton
						icon={<FaMicrophone />}
						size="lg"
						borderRadius="full"
						onClick={handleVoiceRecording}
						className={`telegram-voice-button ${isRecording ? 'recording' : ''}`}
						aria-label={isRecording ? "Stop recording" : "Record voice message"}
						h="44px"
						w="44px"
					/>
				)}
			</Flex>

			{/* Hidden file input */}
			<input
				ref={fileInputRef}
				type="file"
				style={{ display: 'none' }}
				onChange={handleImageChange}
				accept="*/*"
			/>
		</Box>
	);
});

MessageInput.displayName = 'MessageInput';

export default MessageInput;
