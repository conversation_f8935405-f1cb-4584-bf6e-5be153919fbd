import multer from "multer";

// Configure multer for memory storage
const storage = multer.memoryStorage();

// File filter to only allow images
const fileFilter = (req, file, cb) => {
	// Check if the file is an image
	if (file.mimetype.startsWith('image/')) {
		cb(null, true);
	} else {
		cb(new Error('Only image files are allowed!'), false);
	}
};

// Configure multer with options
const upload = multer({
	storage: storage,
	fileFilter: fileFilter,
	limits: {
		fileSize: 5 * 1024 * 1024, // 5MB limit
		files: 1 // Only 1 file per request
	}
});

// Middleware for single image upload
export const uploadSingle = upload.single('img');

// Middleware for multiple file types (img, gif, voice, file)
export const uploadFields = upload.fields([
	{ name: 'img', maxCount: 1 },
	{ name: 'gif', maxCount: 1 },
	{ name: 'voice', maxCount: 1 },
	{ name: 'file', maxCount: 1 }
]);

// Error handling middleware for multer errors
export const handleMulterError = (error, req, res, next) => {
	if (error instanceof multer.MulterError) {
		if (error.code === 'LIMIT_FILE_SIZE') {
			return res.status(400).json({ error: 'File too large. Maximum size is 5MB.' });
		}
		if (error.code === 'LIMIT_FILE_COUNT') {
			return res.status(400).json({ error: 'Too many files. Only 1 file allowed.' });
		}
		if (error.code === 'LIMIT_UNEXPECTED_FILE') {
			return res.status(400).json({ error: 'Unexpected file field.' });
		}
		return res.status(400).json({ error: `Upload error: ${error.message}` });
	}
	
	if (error && error.message === 'Only image files are allowed!') {
		return res.status(400).json({ error: 'Only image files are allowed!' });
	}
	
	next(error);
};

export default upload;
